const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const axios = require('axios');
const NodeCache = require('node-cache');
const TelegramBot = require('node-telegram-bot-api');

// Load environment variables from both server/.env and root .env
require('dotenv').config({ path: path.join(__dirname, '.env') });
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Initialize cache with 24 hour TTL
const cache = new NodeCache({ stdTTL: 86400, checkperiod: 3600 });

// API Keys from environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const TELEGRAM_CHAT_ID = process.env.TELEGRAM_CHAT_ID;

// Initialize Telegram Bot (only if token is provided)
let telegramBot = null;
let subscribedUsers = new Set(); // Store subscribed user chat IDs

if (TELEGRAM_BOT_TOKEN && TELEGRAM_BOT_TOKEN !== 'YOUR_TELEGRAM_BOT_TOKEN_HERE') {
  try {
    telegramBot = new TelegramBot(TELEGRAM_BOT_TOKEN, { polling: true });
    console.log('Telegram bot initialized successfully with polling enabled');

    // Add admin chat ID if provided
    if (TELEGRAM_CHAT_ID && TELEGRAM_CHAT_ID !== 'YOUR_TELEGRAM_CHAT_ID_HERE') {
      subscribedUsers.add(parseInt(TELEGRAM_CHAT_ID));
      console.log(`Admin chat ID ${TELEGRAM_CHAT_ID} added to subscribers`);
    }

    // Handle /start command
    telegramBot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const userName = msg.from.first_name || msg.from.username || 'User';

      subscribedUsers.add(chatId);
      console.log(`New user subscribed: ${userName} (${chatId})`);

      // Save subscribers to database
      try {
        const data = await readDatabase();
        await writeDatabase(data);
      } catch (error) {
        console.error('Error saving subscriber to database:', error);
      }

      const welcomeMessage = `🌟 <b>Welcome to Kubera Horoscope Service!</b> 🌟

Greetings ${userName}!

🔔 <b>You will now receive:</b>
• Instant notifications about new orders
• Special announcements and updates
• Kubera Card order status updates

📱 <b>Useful Commands:</b>
/start - Subscribe to notifications
/stop - Unsubscribe from notifications
/status - Check your notification status

🌐 <b>Website:</b> https://kubera.help

Thank you! 🙏`;

      telegramBot.sendMessage(chatId, welcomeMessage, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
    });

    // Handle /stop command
    telegramBot.onText(/\/stop/, async (msg) => {
      const chatId = msg.chat.id;
      const userName = msg.from.first_name || msg.from.username || 'User';

      subscribedUsers.delete(chatId);
      console.log(`User unsubscribed: ${userName} (${chatId})`);

      // Save subscribers to database
      try {
        const data = await readDatabase();
        await writeDatabase(data);
      } catch (error) {
        console.error('Error saving subscriber changes to database:', error);
      }

      const stopMessage = `👋 <b>Goodbye!</b>

${userName}, you have been unsubscribed from Kubera Horoscope notifications.

🔄 <b>To resubscribe:</b>
Use the /start command

Thank you! 🙏`;

      telegramBot.sendMessage(chatId, stopMessage, {
        parse_mode: 'HTML'
      });
    });

    // Handle /status command
    telegramBot.onText(/\/status/, (msg) => {
      const chatId = msg.chat.id;
      const userName = msg.from.first_name || msg.from.username || 'User';
      const isSubscribed = subscribedUsers.has(chatId);

      const statusMessage = `📊 <b>Your Notification Status</b>

👤 <b>User:</b> ${userName}
🆔 <b>Chat ID:</b> ${chatId}
🔔 <b>Notifications:</b> ${isSubscribed ? '✅ Active' : '❌ Inactive'}
👥 <b>Total Subscribers:</b> ${subscribedUsers.size}

${isSubscribed ?
  '✅ You are receiving notifications!' :
  '❌ Click /start to receive notifications'}`;

      telegramBot.sendMessage(chatId, statusMessage, {
        parse_mode: 'HTML'
      });
    });

    // Handle errors
    telegramBot.on('error', (error) => {
      console.error('Telegram bot error:', error.message);
    });

    // Handle polling errors
    telegramBot.on('polling_error', (error) => {
      console.error('Telegram polling error:', error.message);
    });

  } catch (error) {
    console.error('Failed to initialize Telegram bot:', error.message);
  }
} else {
  console.log('Telegram bot not configured - notifications will be logged only');
}

// Debug environment variables
console.log('Environment variables loaded:');
console.log('GEMINI_API_KEY:', GEMINI_API_KEY ? `${GEMINI_API_KEY.substring(0, 10)}...` : 'NOT FOUND');
console.log('OPENAI_API_KEY:', OPENAI_API_KEY ? `${OPENAI_API_KEY.substring(0, 10)}...` : 'NOT FOUND');
console.log('TELEGRAM_BOT_TOKEN:', TELEGRAM_BOT_TOKEN ? `${TELEGRAM_BOT_TOKEN.substring(0, 10)}...` : 'NOT FOUND');
console.log('TELEGRAM_CHAT_ID:', TELEGRAM_CHAT_ID || 'NOT FOUND');
console.log('PORT:', process.env.PORT);
console.log('NODE_ENV:', process.env.NODE_ENV);

if (!GEMINI_API_KEY && !OPENAI_API_KEY) {
  console.warn('Warning: No API keys found. The server will use fallback horoscopes only.');
}

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // List of allowed origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5000',
      process.env.CORS_ORIGIN || 'https://kubera.help'
    ];

    // Check if origin is in allowed list or is an ngrok URL
    if (allowedOrigins.includes(origin) ||
        origin.includes('ngrok.io') ||
        origin.includes('ngrok-free.app') ||
        origin.includes('ngrok.app')) {
      return callback(null, true);
    }

    // Log rejected origins for debugging
    console.log('CORS rejected origin:', origin);
    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true
}));
app.use(express.json());

// Cache control middleware for API routes
app.use('/api', (req, res, next) => {
  // Disable caching for all API routes
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'ETag': false,
    'Last-Modified': new Date().toUTCString()
  });
  next();
});

// Add versioning header for cache busting
app.use((req, res, next) => {
  res.set('X-App-Version', Date.now().toString());
  next();
});

// API Routes - Define before static file serving

// Order processing endpoint
app.post('/api/orders', async (req, res) => {
  try {
    const { customerInfo, items, totalAmount } = req.body;

    // Validate required fields
    if (!customerInfo || !items || !totalAmount) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: customerInfo, items, totalAmount'
      });
    }

    // Validate customer info
    const requiredFields = ['fullName', 'phone1', 'address', 'city', 'zodiacSign'];
    for (const field of requiredFields) {
      if (!customerInfo[field]) {
        return res.status(400).json({
          success: false,
          error: `Missing required customer field: ${field}`
        });
      }
    }

    // Validate items
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Items must be a non-empty array'
      });
    }

    // Generate order ID
    const orderId = `KUB${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    const timestamp = new Date().toISOString();

    // Create order object
    const orderData = {
      orderId,
      customerInfo,
      items,
      totalAmount,
      timestamp,
      status: 'pending',
      paymentMethod: 'cod'
    };

    // Save to database
    await saveOrderToDatabase(orderData);

    // Send Telegram notification
    const telegramMessage = formatOrderForTelegram(orderData);
    const notificationSent = await sendTelegramNotification(telegramMessage);

    console.log(`New order received: ${orderId}`);
    console.log(`Customer: ${customerInfo.fullName}`);
    console.log(`Items: ${items.length}`);
    console.log(`Total: Rs.${totalAmount}`);
    console.log(`Telegram notification: ${notificationSent ? 'Sent' : 'Failed/Not configured'}`);

    res.json({
      success: true,
      orderId,
      message: 'Order placed successfully',
      telegramNotificationSent: notificationSent
    });

  } catch (error) {
    console.error('Error processing order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process order'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Horoscope API is running',
    timestamp: getSriLankanDateTime().toISOString()
  });
});

// Serve static files from React build
app.use(express.static(path.join(__dirname, '../build')));

// Handle React routing - serve index.html for all non-API routes
app.get('*', (req, res, next) => {
  // Skip API routes
  if (req.path.startsWith('/api')) {
    return next();
  }
  res.sendFile(path.join(__dirname, '../build/index.html'));
});

// JSON Database setup
const dbPath = process.env.DB_PATH || path.join(__dirname, 'horoscope.db');

// Initialize JSON database
async function initializeDatabase() {
  try {
    await fs.access(dbPath);
    console.log('JSON database found at:', dbPath);
  } catch (error) {
    // Create empty database file if it doesn't exist
    const initialData = { horoscopes: [] };
    await fs.writeFile(dbPath, JSON.stringify(initialData, null, 2));
    console.log('Created new JSON database at:', dbPath);
  }
}

// Read data from JSON database
async function readDatabase() {
  try {
    console.log(`[DB] Reading database from: ${dbPath}`);
    const data = await fs.readFile(dbPath, 'utf8');
    const parsed = JSON.parse(data);
    console.log(`[DB] Successfully read database with ${parsed.horoscopes?.length || 0} horoscope entries`);

    // Load subscribers from database
    if (parsed.subscribers && Array.isArray(parsed.subscribers)) {
      parsed.subscribers.forEach(chatId => subscribedUsers.add(chatId));
      console.log(`[DB] Loaded ${parsed.subscribers.length} Telegram subscribers`);
    }

    return parsed;
  } catch (error) {
    console.error('[DB] Error reading database:', error.message);
    console.error('[DB] Database path:', dbPath);
    return { horoscopes: [], orders: [], subscribers: [] };
  }
}

// Write data to JSON database
async function writeDatabase(data) {
  try {
    // Always include current subscribers in the data
    data.subscribers = Array.from(subscribedUsers);
    await fs.writeFile(dbPath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing to database:', error);
    throw error;
  }
}

// Zodiac signs configuration
const zodiacSigns = [
  { id: 'aries', sinhala: 'මේෂ', english: 'Aries', unicode: '♈' },
  { id: 'taurus', sinhala: 'වෘෂභ', english: 'Taurus', unicode: '♉' },
  { id: 'gemini', sinhala: 'මිථුන', english: 'Gemini', unicode: '♊' },
  { id: 'cancer', sinhala: 'කටක', english: 'Cancer', unicode: '♋' },
  { id: 'leo', sinhala: 'සිංහ', english: 'Leo', unicode: '♌' },
  { id: 'virgo', sinhala: 'කන්‍යා', english: 'Virgo', unicode: '♍' },
  { id: 'libra', sinhala: 'තුලා', english: 'Libra', unicode: '♎' },
  { id: 'scorpio', sinhala: 'වෘශ්චික', english: 'Scorpio', unicode: '♏' },
  { id: 'sagittarius', sinhala: 'ධනු', english: 'Sagittarius', unicode: '♐' },
  { id: 'capricorn', sinhala: 'මකර', english: 'Capricorn', unicode: '♑' },
  { id: 'aquarius', sinhala: 'කුම්භ', english: 'Aquarius', unicode: '♒' },
  { id: 'pisces', sinhala: 'මීන', english: 'Pisces', unicode: '♓' }
];

// Utility function to get current Sri Lankan date in YYYY-MM-DD format
function getSriLankanDate() {
  const now = new Date();
  // Get UTC time and add Sri Lankan offset (UTC+5:30)
  const sriLankanTime = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 
    now.getUTCHours() + 5, now.getUTCMinutes() + 30, now.getUTCSeconds());
  return sriLankanTime.toISOString().split('T')[0];
}

// Utility function to get Sri Lankan date object
function getSriLankanDateTime() {
  const now = new Date();
  // Get UTC time and add Sri Lankan offset (UTC+5:30)
  return new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 
    now.getUTCHours() + 5, now.getUTCMinutes() + 30, now.getUTCSeconds());
}

// Helper function to get moon phase
function getMoonPhase(date) {
  const phases = ['අමාවක', 'වැඩෙන අර්ධ චන්ද්‍රය', 'පුර්ණිමා', 'අඩෙන අර්ධ චන්ද්‍රය'];
  const dayOfMonth = date.getDate();
  const phaseIndex = Math.floor((dayOfMonth - 1) / 7.5) % 4;
  return phases[phaseIndex];
}

// Helper function to get seasonal influence
function getSeasonalInfluence(date) {
  const month = date.getMonth() + 1;
  if (month >= 12 || month <= 2) return 'ශීත ඍතුවේ ශක්තිමත් බලපෑම';
  if (month >= 3 && month <= 5) return 'වසන්ත ඍතුවේ නව ආරම්භක ශක්තිය';
  if (month >= 6 && month <= 8) return 'ග්‍රීෂ්ම ඍතුවේ උත්සාහජනක ශක්තිය';
  return 'සරත් ඍතුවේ සමතුලිත ශක්තිය';
}

// Get personalized characteristics for each zodiac sign
function getZodiacCharacteristics(signEnglish) {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const dateNum = today.getDate();
  
  const characteristics = {
    'Aries': {
      traits: 'මේෂ රාශියේ අය ස්වභාවයෙන්ම නායකත්ව ගුණ, ධෛර්යය, ශක්තිමත්කම සහ ක්‍රියාශීලීභාවය ඇති අයයි. ඔවුන් නව ආරම්භයන්ට ප්‍රිය කරන අතර, අභියෝගවලට මුහුණ දීමට භීතියක් නැත. කෙසේ වුවද, ඉක්මන් කෝපයක් සහ ඉවසිල්ල නොමැතිකම ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'අඟහරු ග්‍රහයා (Mars) මේෂ රාශියේ අධිපති ග්‍රහයා වන අතර, එය ශක්තිය, ධෛර්යය, සටන්කාමීත්වය සහ ක්‍රියාශීලීභාවය නියෝජනය කරයි. අඟහරුගේ බලපෑම නිසා මේෂ රාශියේ අයට ප්‍රබල නායකත්ව ගුණ ඇත.',
      todaysInfluence: `අද දිනයේ අඟහරු ග්‍රහයාගේ ශක්තිමත් බලපෑමක් ඇති අතර, ${dayOfWeek < 3 ? 'සතියේ ආරම්භයේ ශක්තිය' : dayOfWeek < 5 ? 'සතියේ මැද ස්ථාවරත්වය' : 'සතියේ අවසානයේ විවේකය'} ඔබේ ක්‍රියාකාරකම්වලට බලපානු ඇත. ${dateNum % 2 === 0 ? 'යුගල දිනයක් වීම නිසා සමබරතාවය වැදගත්' : 'ඔත්තේ දිනයක් වීම නිසා ක්‍රියාශීලීභාවය ඉහළ'} වේ.`
    },
    'Taurus': {
      traits: 'වෘෂභ රාශියේ අය ස්ථාවර, ඉවසිලිවන්ත, විශ්වාසදායක සහ ප්‍රායෝගික ස්වභාවයක් ඇති අයයි. ඔවුන් සුන්දරත්වයට, සුවපහසුවට සහ ස්ථාවරත්වයට ප්‍රිය කරන අතර, වෙනස්කම්වලට මන්දගාමීව අනුගත වේ. කෙසේ වුවද, මුරණ්ඩුකම සහ භෞතික දේවලට අධික ලැදිකම ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'සිකුරු ග්‍රහයා (Venus) වෘෂභ රාශියේ අධිපති ග්‍රහයා වන අතර, එය ආදරය, සුන්දරත්වය, කලාව, සුවපහසුව සහ භෞතික සම්පත් නියෝජනය කරයි. සිකුරුගේ බලපෑම නිසා වෘෂභ රාශියේ අයට කලාත්මක හැකියාවන් ඇත.',
      todaysInfluence: `අද දිනයේ සිකුරු ග්‍රහයාගේ මෘදු බලපෑමක් ඇති අතර, ${dayOfWeek === 5 ? 'සිකුරාදා වීම නිසා විශේෂ ශුභත්වයක්' : 'සිකුරුගේ ආශීර්වාදය'} ලැබෙනු ඇත. ${dateNum % 3 === 0 ? 'තුනේ ගුණයක් වීම නිසා සමගිය වැදගත්' : 'ස්ථාවරත්වය සහ ඉවසිල්ල'} අද ප්‍රධාන වේ.`
    },
    'Gemini': {
      traits: 'මිථුන රාශියේ අය බුද්ධිමත්, කතාබහට ප්‍රිය, කුතුහලශීලී සහ අනුවර්තනය වීමේ හැකියාව ඇති අයයි. ඔවුන් නව දේවල් ඉගෙන ගැනීමට, සන්නිවේදනයට සහ සමාජ කටයුතුවලට ප්‍රිය කරයි. කෙසේ වුවද, අස්ථාවරත්වය සහ තීරණ ගැනීමේදී දුර්වලතාවය ඔවුන්ගේ ගැටළු වේ.',
      planetaryInfluence: 'බුධ ග්‍රහයා (Mercury) මිථුන රාශියේ අධිපති ග්‍රහයා වන අතර, එය සන්නිවේදනය, බුද්ධිය, ඉගෙනීම, ගමන් සහ තාක්ෂණය නියෝජනය කරයි. බුධගේ බලපෑම නිසා මිථුන රාශියේ අයට විශිෂ්ට සන්නිවේදන කුසලතා ඇත.',
      todaysInfluence: `අද දිනයේ බුධ ග්‍රහයාගේ ක්‍රියාශීලී බලපෑමක් ඇති අතර, ${dayOfWeek === 3 ? 'බදාදා වීම නිසා බුධගේ විශේෂ ආශීර්වාදය' : 'සන්නිවේදන ශක්තිය ඉහළ'} වේ. ${dateNum % 2 === 1 ? 'ඔත්තේ දිනයක් වීම නිසා මානසික ක්‍රියාශීලීභාවය වැඩි' : 'චින්තනය සහ සැලසුම් කිරීම'} වැදගත් වේ.`
    },
    'Cancer': {
      traits: 'කටක රාශියේ අය හැඟීම්බර, රැකවරණශීලී, අනුකම්පාශීලී සහ පවුල් ප්‍රිය ස්වභාවයක් ඇති අයයි. ඔවුන් ගෘහස්ථ ජීවිතයට, සම්ප්‍රදායන්ට සහ ආරක්ෂාවට ප්‍රිය කරයි. කෙසේ වුවද, අධික සංවේදීත්වය සහ මනෝභාව වෙනස්වීම් ඔවුන්ගේ අභියෝග වේ.',
      planetaryInfluence: 'චන්ද්‍රයා (Moon) කටක රාශියේ අධිපති ග්‍රහයා වන අතර, එය හැඟීම්, මනෝභාවයන්, මාතෘත්වය, ගෘහස්ථ ජීවිතය සහ අතීත සිහිකිරීම් නියෝජනය කරයි. චන්ද්‍රයාගේ බලපෑම නිසා කටක රාශියේ අයට ප්‍රබල අන්තර්ඥානයක් ඇත.',
      todaysInfluence: `අද දිනයේ චන්ද්‍රයාගේ ${today.getDate() < 15 ? 'වර්ධනය වන' : 'අඩුවන'} කලාව අනුව හැඟීම්බර ශක්තිය ${dayOfWeek === 1 ? 'සඳුදා වීම නිසා චන්ද්‍රයාගේ විශේෂ බලපෑමක්' : 'මෘදු බලපෑමක්'} ඇත. ${dateNum % 4 === 0 ? 'සතරේ ගුණයක් වීම නිසා ස්ථාවරත්වය' : 'හැඟීම්බර සමබරතාවය'} වැදගත් වේ.`
    },
    'Leo': {
      traits: 'සිංහ රාශියේ අය ආත්මවිශ්වාසයෙන්, නායකත්වයෙන්, උදාරත්වයෙන් සහ නාට්‍යමය ස්වභාවයකින් යුක්ත අයයි. ඔවුන් අවධානයට, ගෞරවයට සහ නිර්මාණශීලීත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අහංකාරය සහ අධික ආත්මප්‍රේමය ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'සූර්යයා (Sun) සිංහ රාශියේ අධිපති ග්‍රහයා වන අතර, එය ජීවිතය, ශක්තිය, නායකත්වය, ආත්මවිශ්වාසය සහ නිර්මාණශීලීත්වය නියෝජනය කරයි. සූර්යයාගේ බලපෑම නිසා සිංහ රාශියේ අයට ස්වභාවික නායකත්ව ගුණ ඇත.',
      todaysInfluence: `අද දිනයේ සූර්යයාගේ ප්‍රබල බලපෑමක් ඇති අතර, ${dayOfWeek === 0 ? 'ඉරිදා වීම නිසා සූර්යයාගේ සම්පූර්ණ ශක්තිය' : 'සූර්ය ශක්තිය ඉහළ මට්ටමක'} පවතිනු ඇත. ${dateNum % 5 === 0 ? 'පහේ ගුණයක් වීම නිසා නායකත්ව ගුණ ප්‍රකාශ' : 'ආත්මවිශ්වාසය සහ නිර්මාණශීලීත්වය'} වැදගත් වේ.`
    },
    'Virgo': {
      traits: 'කන්‍යා රාශියේ අය විස්තරාත්මක, ක්‍රමවත්, විශ්ලේෂණාත්මක සහ සේවා කිරීමේ ආකල්පයක් ඇති අයයි. ඔවුන් පරිපූර්ණත්වයට, ක්‍රමවත්කමට සහ ප්‍රායෝගිකත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අධික විවේචනාත්මකත්වය සහ කනස්සල්ල ඔවුන්ගේ ගැටළු වේ.',
      planetaryInfluence: 'බුධ ග්‍රහයා (Mercury) කන්‍යා රාශියේ අධිපති ග්‍රහයා වන අතර, එය විශ්ලේෂණය, විස්තර, සෞඛ්‍යය, සේවය සහ ප්‍රායෝගිකත්වය නියෝජනය කරයි. බුධගේ බලපෑම නිසා කන්‍යා රාශියේ අයට විශිෂ්ට විශ්ලේෂණ කුසලතා ඇත.',
      todaysInfluence: `අද දිනයේ බුධ ග්‍රහයාගේ ප්‍රායෝගික බලපෑමක් ඇති අතර, ${dayOfWeek === 3 ? 'බදාදා වීම නිසා බුධගේ විශේෂ ආශීර්වාදය' : 'විශ්ලේෂණාත්මක ශක්තිය ඉහළ'} වේ. ${dateNum % 6 === 0 ? 'හයේ ගුණයක් වීම නිසා සේවා කිරීමේ ආකල්පය' : 'ක්‍රමවත්කම සහ විස්තරාත්මකත්වය'} වැදගත් වේ.`
    },
    'Libra': {
      traits: 'තුලා රාශියේ අය සමබරත්වය, සාධාරණත්වය, සුන්දරත්වය සහ සමගිය ප්‍රිය කරන අයයි. ඔවුන් සම්බන්ධතාවලට, කලාවට සහ සාමයට ප්‍රිය කරයි. කෙසේ වුවද, අනිශ්චිතභාවය සහ ගැටුම් වළකීම ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'සිකුරු ග්‍රහයා (Venus) තුලා රාශියේ අධිපති ග්‍රහයා වන අතර, එය සම්බන්ධතා, සමබරත්වය, සාධාරණත්වය, සුන්දරත්වය සහ සමගිය නියෝජනය කරයි. සිකුරුගේ බලපෑම නිසා තුලා රාශියේ අයට සම්බන්ධතා පවත්වා ගැනීමේ හැකියාව ඇත.',
      todaysInfluence: `අද දිනයේ සිකුරු ග්‍රහයාගේ සමබර බලපෑමක් ඇති අතර, ${dayOfWeek === 5 ? 'සිකුරාදා වීම නිසා සිකුරුගේ සම්පූර්ණ ශක්තිය' : 'සමගිය සහ සුන්දරත්වය'} ප්‍රකාශ වේ. ${dateNum % 7 === 0 ? 'සතේ ගුණයක් වීම නිසා සම්පූර්ණත්වය' : 'සමබරත්වය සහ සාධාරණත්වය'} වැදගත් වේ.`
    },
    'Scorpio': {
      traits: 'වෘශ්චික රාශියේ අය තීව්‍ර, ගුප්ත, දෘඩනිශ්චයී සහ පරිවර්තනයට ප්‍රිය කරන අයයි. ඔවුන් ගැඹුරු සත්‍යයන්ට, ගුප්ත දේවලට සහ ආධ්‍යාත්මික පරිවර්තනයට ප්‍රිය කරයි. කෙසේ වුවද, පළිගැනීමේ ආකල්පය සහ අධික සැකයන් ඔවුන්ගේ ගැටළු වේ.',
      planetaryInfluence: 'අඟහරු ග්‍රහයා (Mars) සහ ප්ලූටෝ (Pluto) වෘශ්චික රාශියේ අධිපති ග්‍රහයන් වන අතර, ඒවා පරිවර්තනය, ගුප්ත ශක්තිය, තීව්‍රත්වය සහ පුනර්ජනනය නියෝජනය කරයි. මෙම ග්‍රහ බලපෑම නිසා වෘශ්චික රාශියේ අයට ගැඹුරු අන්තර්ඥානයක් ඇත.',
      todaysInfluence: `අද දිනයේ අඟහරු සහ ප්ලූටෝගේ සංයුක්ත බලපෑමක් ඇති අතර, ${dayOfWeek === 2 ? 'අඟහරුවාදා වීම නිසා අඟහරුගේ විශේෂ ශක්තිය' : 'පරිවර්තන ශක්තිය ඉහළ'} වේ. ${dateNum % 8 === 0 ? 'අටේ ගුණයක් වීම නිසා අනන්තත්වය' : 'තීව්‍රත්වය සහ ගැඹුරු චින්තනය'} වැදගත් වේ.`
    },
    'Sagittarius': {
      traits: 'ධනු රාශියේ අය ස්වාධීන, ඔප්තිමිස්ටික්, ගවේෂණාත්මක සහ දර්ශනික ස්වභාවයක් ඇති අයයි. ඔවුන් ගමන්, ඉගෙනීම, ආගම සහ ස්වාධීනත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අධික අවිනීතභාවය සහ අනුකම්පා නොමැතිකම ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'ගුරු ග්‍රහයා (Jupiter) ධනු රාශියේ අධිපති ග්‍රහයා වන අතර, එය ප්‍රඥාව, ඉගෙනීම, ආගම, ගමන් සහ ව්‍යාප්තිය නියෝජනය කරයි. ගුරුගේ බලපෑම නිසා ධනු රාශියේ අයට ප්‍රඥාව සහ ඔප්තිමිස්ටික් දෘෂ්ටිකෝණයක් ඇත.',
      todaysInfluence: `අද දිනයේ ගුරු ග්‍රහයාගේ ව්‍යාප්ත බලපෑමක් ඇති අතර, ${dayOfWeek === 4 ? 'ගුරුවාදා වීම නිසා ගුරුගේ සම්පූර්ණ ආශීර්වාදය' : 'ප්‍රඥාව සහ ඉගෙනීම'} ප්‍රකාශ වේ. ${dateNum % 9 === 0 ? 'නවයේ ගුණයක් වීම නිසා සම්පූර්ණත්වය' : 'ගවේෂණය සහ ස්වාධීනත්වය'} වැදගත් වේ.`
    },
    'Capricorn': {
      traits: 'මකර රාශියේ අය අභිලාෂකාමී, ප්‍රායෝගික, වගකිවයුතු සහ සම්ප්‍රදායික ස්වභාවයක් ඇති අයයි. ඔවුන් සාර්ථකත්වයට, ගෞරවයට සහ ස්ථාවරත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අධික කටුකම සහ භෞතිකවාදය ඔවුන්ගේ ගැටළු වේ.',
      planetaryInfluence: 'සෙනසුරු ග්‍රහයා (Saturn) මකර රාශියේ අධිපති ග්‍රහයා වන අතර, එය විනය, වගකීම්, ව්‍යුහය, කාලය සහ ජීවිත පාඩම් නියෝජනය කරයි. සෙනසුරුගේ බලපෑම නිසා මකර රාශියේ අයට ප්‍රබල විනයක් ඇත.',
      todaysInfluence: `අද දිනයේ සෙනසුරු ග්‍රහයාගේ ව්‍යුහගත බලපෑමක් ඇති අතර, ${dayOfWeek === 6 ? 'සෙනසුරාදා වීම නිසා සෙනසුරුගේ විශේෂ ශක්තිය' : 'විනය සහ වගකීම්'} ප්‍රකාශ වේ. ${dateNum % 10 === 0 ? 'දහයේ ගුණයක් වීම නිසා සම්පූර්ණත්වය' : 'ක්‍රමානුකූලත්වය සහ ඉවසිල්ල'} වැදගත් වේ.`
    },
    'Aquarius': {
      traits: 'කුම්භ රාශියේ අය නවීන, ස්වාධීන, මානවහිතකාමී සහ අනාගතවාදී ස්වභාවයක් ඇති අයයි. ඔවුන් නවෝත්පාදනයට, සමාජ සේවයට සහ ස්වාධීනත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අධික වෙන්වීම සහ අනපේක්ෂිතභාවය ඔවුන්ගේ දුර්වලතා වේ.',
      planetaryInfluence: 'සෙනසුරු ග්‍රහයා (Saturn) සහ යුරේනස් (Uranus) කුම්භ රාශියේ අධිපති ග්‍රහයන් වන අතර, ඒවා නවෝත්පාදනය, ස්වාධීනත්වය, මානවහිතකාමීත්වය සහ අනාගතය නියෝජනය කරයි. මෙම ග්‍රහ බලපෑම නිසා කුම්භ රාශියේ අයට අනාගතවාදී දෘෂ්ටිකෝණයක් ඇත.',
      todaysInfluence: `අද දිනයේ සෙනසුරු සහ යුරේනස්ගේ සංයුක්ත බලපෑමක් ඇති අතර, ${dayOfWeek === 6 ? 'සෙනසුරාදා වීම නිසා සෙනසුරුගේ ස්ථාවර ශක්තිය' : 'නවෝත්පාදන ශක්තිය ඉහළ'} වේ. ${dateNum % 11 === 0 ? 'එකොළහේ ගුණයක් වීම නිසා අනන්යතාව' : 'ස්වාධීනත්වය සහ මානවහිතකාමීත්වය'} වැදගත් වේ.`
    },
    'Pisces': {
      traits: 'මීන රාශියේ අය සංවේදී, අනුකම්පාශීලී, කලාත්මක සහ ආධ්‍යාත්මික ස්වභාවයක් ඇති අයයි. ඔවුන් සිහින, කලාව, සේවය සහ ආධ්‍යාත්මිකත්වයට ප්‍රිය කරයි. කෙසේ වුවද, අධික සංවේදීත්වය සහ යථාර්ථයෙන් පලා යාම ඔවුන්ගේ ගැටළු වේ.',
      planetaryInfluence: 'ගුරු ග්‍රහයා (Jupiter) සහ නෙප්චූන් (Neptune) මීන රාශියේ අධිපති ග්‍රහයන් වන අතර, ඒවා අනුකම්පාව, ආධ්‍යාත්මිකත්වය, සිහින සහ අන්තර්ඥානය නියෝජනය කරයි. මෙම ග්‍රහ බලපෑම නිසා මීන රාශියේ අයට ප්‍රබල අන්තර්ඥානයක් ඇත.',
      todaysInfluence: `අද දිනයේ ගුරු සහ නෙප්චූන්ගේ ආධ්‍යාත්මික බලපෑමක් ඇති අතර, ${dayOfWeek === 4 ? 'ගුරුවාදා වීම නිසා ගුරුගේ ආශීර්වාදය' : 'අන්තර්ඥාන ශක්තිය ඉහළ'} වේ. ${dateNum % 12 === 0 ? 'දොළහේ ගුණයක් වීම නිසා සම්පූර්ණ චක්‍රය' : 'සංවේදීත්වය සහ අනුකම්පාව'} වැදගත් වේ.`
    }
  };
  
  return characteristics[signEnglish] || characteristics['Aries'];
}

// Function to clear today's horoscope data and fetch fresh data
async function clearTodayDataAndRefetch() {
  try {
    const today = getSriLankanDate();
    console.log(`Clearing horoscope data for ${today} and fetching fresh data...`);
    
    const data = await readDatabase();
    
    // Remove all horoscopes for today
    const originalCount = data.horoscopes.length;
    data.horoscopes = data.horoscopes.filter(h => h.date_created !== today);
    const removedCount = originalCount - data.horoscopes.length;
    
    await writeDatabase(data);
    console.log(`Removed ${removedCount} horoscope entries for ${today}`);
    
    // Fetch fresh horoscopes for all signs
    console.log('Fetching fresh horoscopes for all zodiac signs...');
    await fetchAndSaveAllHoroscopes();
    
    console.log('Successfully cleared old data and fetched fresh horoscopes!');
    return { success: true, removedCount, date: today };
  } catch (error) {
    console.error('Error clearing data and refetching:', error);
    throw error;
  }
}

// Enhanced Gemini API call with improved prompt for fresh daily content
async function getHoroscopeFromGemini(signEnglish, signSinhala) {
  try {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not found');
    }

    // Get current date in Sri Lankan timezone with detailed formatting
    const sriLankanDate = getSriLankanDateTime();
    const dateString = sriLankanDate.toLocaleDateString('si-LK', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    });
    
    // Generate unique daily elements for fresh content
    const timeStamp = Date.now();
    const dailyNumber = (sriLankanDate.getDate() + sriLankanDate.getMonth() + sriLankanDate.getFullYear()) % 100;
    const moonPhase = getMoonPhase(sriLankanDate);
    const seasonalInfluence = getSeasonalInfluence(sriLankanDate);

    // Get personalized characteristics for each zodiac sign
    const signCharacteristics = getZodiacCharacteristics(signEnglish);
    
    const enhancedPrompt = `ශ්‍රී ලංකාවේ ප්‍රසිද්ධ ජ්‍යොතිෂ ශාස්ත්‍රඥයෙකු ලෙස, ${signEnglish} (${signSinhala}) රාශියේ අයට ${dateString} දිනය සඳහා ඉතා විස්තරාත්මක සහ සම්පූර්ණ රාශිඵලයක් සකස් කරන්න.

**අද දිනයේ විශේෂ තත්ත්වයන්:**
- දිනය: ${dateString}
- චන්ද්‍ර කලාව: ${moonPhase}
- සෘතු බලපෑම: ${seasonalInfluence}
- දිනයේ ශක්ති අංකය: ${dailyNumber}
- අද දිනයේ අනන්‍ය ශක්ති කේතය: ${timeStamp.toString().slice(-6)}

**${signEnglish} රාශියේ ලක්ෂණ සහ විශේෂත්ව:**
${signCharacteristics.traits}

**ග්‍රහ බලපෑම්:**
${signCharacteristics.planetaryInfluence}

**අද දිනයේ විශේෂ ග්‍රහ තත්ත්වය:**
${signCharacteristics.todaysInfluence}

මෙම ${signEnglish} රාශියේ ස්වභාවික ලක්ෂණ, අද දිනයේ ග්‍රහ බලපෑම්, චන්ද්‍ර කලාව සහ සෘතු බලපෑම් සැලකිල්ලට ගෙන, පහත සඳහන් සෑම කාණ්ඩයක්ම සඳහා අවම වශයෙන් 500-700 අක්ෂර (4-5 වාක්‍ය) සහිත සම්පූර්ණ සහ අර්ථවත් අන්තර්ගතයක් ලබා දෙන්න. **අද දිනයේ දිනය සහ කාලය නිරන්තරයෙන් සඳහන් කරන්න:**

**1. ආදරය සහ සම්බන්ධතා:**
[${dateString} දිනයේ ප්‍රේම සම්බන්ධතා, විවාහ ජීවිතය, පවුලේ සාමාජිකයන් සමඟ සම්බන්ධතා, මිත්‍රත්වයන්, හැඟීම්බර ගැටළු, නව ආදර අවස්ථා, සහකරු/සහකාරිය සමඟ අවබෝධය ගැන අද දිනයට විශේෂිත විස්තරාත්මක උපදෙස්. අද දිනයේ චන්ද්‍ර කලාව සහ ග්‍රහ බලපෑම් ආදරයට කෙසේ බලපාන්නේද යන්න ඇතුළත් කරන්න.]

**2. වෘත්තීය ජීවිතය:**
[${dateString} දිනයේ රැකියාව, ව්‍යාපාර, වෘත්තීය ප්‍රගතිය, සේවා ස්ථානයේ සම්බන්ධතා, නව රැකියා අවස්ථා, ව්‍යාපාරික තීරණ, සේවා ස්ථානයේ ගැටළු, කුසලතා වර්ධනය, ප්‍රවර්ධන අවස්ථා, සේවා ස්ථානයේ නායකත්වය, සහකරුවන් සමඟ සම්බන්ධතා, ව්‍යාපාරික ගනුදෙනු, නව ව්‍යාපාර ආරම්භ කිරීම, වෘත්තීය අභියෝග, කාර්ය සාධනය, වැටුප් වර්ධනය, වෘත්තීය පුහුණුව, නව තාක්ෂණික දැනුම ඉගෙනීම, වෘත්තීය ජාලකරණය, සේවා ස්ථාන වෙනස් කිරීම, ස්වකීය ව්‍යාපාර ආරම්භ කිරීම, වෘත්තීය ඉලක්ක සැලසුම් කිරීම ගැන අද දිනයට නිශ්චිත සවිස්තර මග පෙන්වීම්. අද දිනයේ ග්‍රහ තත්ත්වය වෘත්තීය කටයුතුවලට කෙසේ බලපාන්නේද, වෘත්තීය සාර්ථකත්වය සඳහා අද දිනයේ කළ යුතු නිශ්චිත ක්‍රියාමාර්ග, වෘත්තීය අභියෝග මඟහරවා ගැනීමේ ක්‍රම, සේවා ස්ථානයේ ධනාත්මක බලපෑමක් ඇති කිරීමේ ක්‍රම යන්න සඳහන් කරන්න. වෘත්තීය ජීවිතයේ දිගුකාලීන සාර්ථකත්වය සඳහා අද දිනයේ ගත යුතු පියවර ඇතුළත් කරන්න.]

**3. සෞඛ්‍ය සහ යහපැවැත්ම:**
[${dateString} දිනයේ ශාරීරික සෞඛ්‍යය, මානසික යහපැවැත්ම, ශක්ති මට්ටම්, ආහාර පාන, ව්‍යායාම, විවේකය, ආතතිය කළමනාකරණය, රෝග ප්‍රතිකාර, සෞඛ්‍ය පරීක්ෂණ ගැන අද දිනයට විශේෂිත සම්පූර්ණ උපදෙස්. අද දිනයේ චන්ද්‍ර කලාව සහ සෘතු බලපෑම් සෞඛ්‍යයට කෙසේ බලපාන්නේද යන්න ඇතුළත් කරන්න.]

**4. මූල්‍ය කටයුතු:**
[${dateString} දිනයේ ආදායම, වියදම්, ඉතිරිකිරීම්, ආයෝජන, ණය, මූල්‍ය සැලසුම්, ව්‍යාපාරික ආදායම්, අනපේක්ෂිත වියදම්, මූල්‍ය තීරණ, ධන ලාභ අවස්ථා ගැන අද දිනයට නිශ්චිත සවිස්තර මග පෙන්වීම්. අද දිනයේ ග්‍රහ බලපෑම් මූල්‍ය කටයුතුවලට කෙසේ බලපාන්නේද යන්න සඳහන් කරන්න.]

**5. සාමාන්‍ය උපදෙස්:**
[${dateString} දිනයේ ආධ්‍යාත්මික මග පෙන්වීම්, වාසනාවන්ත වර්ණ සහ අංක, වාසනාවන්ත කාල, ගමන්, අධ්‍යාපනය, නීතිමය කටයුතු, පවුලේ සිදුවීම්, සමාජ ක්‍රියාකාරකම්, අද දිනය සඳහා සම්පූර්ණ සාමාන්‍ය උපදෙස්. අද දිනයේ සමස්ත ශක්ති ප්‍රවාහය සහ කළ යුතු/නොකළ යුතු දේවල් ඇතුළත් කරන්න.]

**අනිවාර්ය අවශ්‍යතා:** 
- සෑම කාණ්ඩයක්ම ආරම්භ කරන්න "${dateString} දිනයේ" යන වචන වලින්
- සෑම කාණ්ඩයක්ම සඳහා සම්පූර්ණ, අර්ථවත්, අද දිනයට විශේෂිත අන්තර්ගතයක් ලියන්න
- කිසිදු කාණ්ඩයක් හිස් හෝ අසම්පූර්ණ නොකරන්න
- ශ්‍රී ලංකාවේ සංස්කෘතියට ගැලපෙන ආකාරයෙන් ලියන්න
- ධනාත්මක සහ ආධ්‍යාත්මික ස්වරයක් භාවිතා කරන්න
- සුන්දර සිංහල භාෂාවෙන් ලියන්න
- අද දිනය සඳහා නිශ්චිත, ක්‍රියාත්මක උපදෙස් ඇතුළත් කරන්න
- සෑම කාණ්ඩයකම අද දිනයේ දිනය සහ කාලය සඳහන් කරන්න
- අද දිනයේ අනන්‍ය ශක්ති කේතය (${timeStamp.toString().slice(-6)}) භාවිතා කර අද දිනයට විශේෂිත අන්තර්ගතයක් නිර්මාණය කරන්න`;

    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`,
      {
        contents: [{
          parts: [{
            text: enhancedPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4096
        }
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    if (response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {
      return response.data.candidates[0].content.parts[0].text;
    } else {
      throw new Error('Invalid response from Gemini API');
    }
  } catch (error) {
    console.error('Gemini API error:', error.message);
    throw error;
  }
}

// Parse horoscope content into categories
function parseHoroscopeContent(rawContent) {
  const categories = {
    love: '',
    career: '',
    health: '',
    finance: '',
    general: ''
  };

  try {
    // Split by numbered sections (1., 2., 3., etc.) with asterisks
    const numberedPattern = /\*\*\d+\.\s*[^\*]+:\*\*/g;
    const sections = rawContent.split(numberedPattern);
    const headers = rawContent.match(numberedPattern) || [];
    
    headers.forEach((header, index) => {
      const content = sections[index + 1] ? sections[index + 1].trim() : '';
      if (!content) return;
      
      const headerText = header.toLowerCase();
      let cleanContent = content;
      
      // Remove leading/trailing asterisks and colons
      cleanContent = cleanContent.replace(/^\*\*|\*\*$/g, '').replace(/^:|:$/g, '').trim();
      
      // Categorize based on section title
      if (headerText.includes('ආදර') || headerText.includes('සම්බන්ධතා') || headerText.includes('ප්‍රේම')) {
        categories.love = cleanContent;
      } else if (headerText.includes('වෘත්ති') || headerText.includes('වෘත්තීය') || headerText.includes('රැකියා') || headerText.includes('ව්‍යාපාර')) {
        categories.career = cleanContent;
      } else if (headerText.includes('සෞඛ්‍ය') || headerText.includes('යහපැවැත්ම')) {
        categories.health = cleanContent;
      } else if (headerText.includes('මූල්‍ය') || headerText.includes('ආර්ථික') || headerText.includes('ධන')) {
        categories.finance = cleanContent;
      } else if (headerText.includes('සාමාන්‍ය') || headerText.includes('උපදෙස්')) {
        categories.general = cleanContent;
      }
    });
    
    // If numbered sections didn't work, try alternative parsing
    if (Object.values(categories).every(cat => !cat)) {
      // Try splitting by section headers with asterisks
      const headerPattern = /\*\*[^\*]+:\*\*/g;
      const parts = rawContent.split(headerPattern);
      const headers = rawContent.match(headerPattern) || [];
      
      headers.forEach((header, index) => {
        const content = parts[index + 1] ? parts[index + 1].trim() : '';
        if (!content) return;
        
        const lowerHeader = header.toLowerCase();
        
        if (lowerHeader.includes('ආදර') || lowerHeader.includes('සම්බන්ධතා') || lowerHeader.includes('ප්‍රේම')) {
          categories.love = content;
        } else if (lowerHeader.includes('වෘත්ති') || lowerHeader.includes('රැකියා') || lowerHeader.includes('ව්‍යාපාර')) {
          categories.career = content;
        } else if (lowerHeader.includes('සෞඛ්‍ය') || lowerHeader.includes('යහපැවැත්ම')) {
          categories.health = content;
        } else if (lowerHeader.includes('මූල්‍ය') || lowerHeader.includes('ආර්ථික') || lowerHeader.includes('ධන')) {
          categories.finance = content;
        } else if (lowerHeader.includes('සාමාන්‍ය') || lowerHeader.includes('උපදෙස්')) {
          categories.general = content;
        }
      });
    }
    
    // Fill empty categories with generic content
    Object.keys(categories).forEach(key => {
      if (!categories[key]) {
        categories[key] = getGenericContent(key);
      }
    });
    
  } catch (error) {
    console.error('Error parsing horoscope content:', error);
    // Return generic content for all categories
    Object.keys(categories).forEach(key => {
      categories[key] = getGenericContent(key);
    });
  }
  
  return categories;
}

// Get generic content for categories
function getGenericContent(category) {
  const genericContent = {
    love: 'අද ඔබේ ආදර ජීවිතයේ ධනාත්මක වෙනස්කම් සිදුවිය හැකිය. සහකරු/සහකාරිය සමඟ අවබෝධයෙන් කටයුතු කරන්න.',
    career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා ලැබිය හැකිය. ඔබේ කුසලතා වර්ධනය කිරීමට අවධානය යොමු කරන්න.',
    health: 'සෞඛ්‍ය තත්ත්වය සාමාන්‍යයෙන් හොඳ වනු ඇත. නිරෝගී ආහාර පාන සහ ව්‍යායාම කිරීමට අවධානය යොමු කරන්න.',
    finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න. අනවශ්‍ය වියදම් වළකින්න සහ ඉතිරිකිරීම් වැඩි කරන්න.',
    general: 'සමස්තයක් වශයෙන් අද ඔබට හොඳ දිනයක් වනු ඇත. ධනාත්මක චින්තනයෙන් ඉදිරියට යන්න.'
  };
  
  return genericContent[category] || 'අද ඔබට සාර්ථක දිනයක් වේවා!';
}

// Save horoscope to JSON database
async function saveHoroscopeToDatabase(signData, horoscopeContent, rawContent) {
  try {
    const today = getSriLankanDate();
    const data = await readDatabase();
    
    // Remove existing entry for this sign and date
    data.horoscopes = data.horoscopes.filter(
      h => !(h.sign_id === signData.id && h.date_created === today)
    );
    
    // Add new horoscope entry
    const newHoroscope = {
      id: Date.now(), // Simple ID generation
      sign_id: signData.id,
      sign_english: signData.english,
      sign_sinhala: signData.sinhala,
      love_content: horoscopeContent.love,
      career_content: horoscopeContent.career,
      health_content: horoscopeContent.health,
      finance_content: horoscopeContent.finance,
      general_content: horoscopeContent.general,
      raw_content: rawContent,
      date_created: today,
      created_at: getSriLankanDateTime().toISOString()
    };
    
    data.horoscopes.push(newHoroscope);
    await writeDatabase(data);
    
    return newHoroscope.id;
  } catch (error) {
    console.error('Error saving horoscope:', error);
    throw error;
  }
}

// Fetch and save horoscope for a single sign
async function fetchAndSaveHoroscope(signData) {
  try {
    console.log(`[FETCH] Starting horoscope generation for ${signData.english} (${signData.sinhala})`);
    
    let rawContent;
    try {
      console.log(`[FETCH] Attempting to get horoscope from Gemini for ${signData.english}`);
      rawContent = await getHoroscopeFromGemini(signData.english, signData.sinhala);
      console.log(`[FETCH] Successfully got content from Gemini for ${signData.english}`);
    } catch (error) {
      console.log(`[FETCH] Gemini failed for ${signData.english}, using fallback content:`, error.message);
      rawContent = getGenericContent('general');
    }
    
    console.log(`[FETCH] Parsing content for ${signData.english}`);
    const parsedContent = parseHoroscopeContent(rawContent);
    
    console.log(`[FETCH] Saving to database for ${signData.english}`);
    const horoscopeId = await saveHoroscopeToDatabase(signData, parsedContent, rawContent);
    
    console.log(`[FETCH] Successfully saved horoscope for ${signData.english} with ID: ${horoscopeId}`);
    return horoscopeId;
  } catch (error) {
    console.error(`[FETCH] Error processing horoscope for ${signData.english}:`, error.message);
    console.error(`[FETCH] Error stack:`, error.stack);
    throw error;
  }
}

// Fetch and save all horoscopes
async function fetchAndSaveAllHoroscopes() {
  const sriLankanTime = getSriLankanDateTime();
  console.log('Starting horoscope fetch at', sriLankanTime.toISOString(), '(Sri Lankan time)');
  
  try {
    const today = getSriLankanDate();
    const data = await readDatabase();
    const todayHoroscopes = data.horoscopes.filter(h => h.date_created === today);
    const existingSigns = new Set(todayHoroscopes.map(h => h.sign_id));
    
    // Only fetch missing signs
    const missingSignsCount = zodiacSigns.length - existingSigns.size;
    if (missingSignsCount > 0) {
      console.log(`Fetching ${missingSignsCount} missing horoscope(s)...`);
      
      for (const sign of zodiacSigns) {
        if (!existingSigns.has(sign.id)) {
          console.log(`Fetching horoscope for ${sign.english}...`);
          await fetchAndSaveHoroscope(sign);
          // Add delay between requests to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    } else {
      console.log('All horoscopes for today are already available.');
    }
  } catch (error) {
    console.error('Error in fetchAndSaveAllHoroscopes:', error);
    // Fallback: fetch all signs
    console.log('Falling back to fetch all signs...');
    for (const sign of zodiacSigns) {
      await fetchAndSaveHoroscope(sign);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('Horoscope fetch completed at', getSriLankanDateTime().toISOString(), '(Sri Lankan time)');
}

// Schedule daily horoscope fetch at 00:00 Sri Lankan time (GMT+5:30)
cron.schedule('0 0 * * *', () => {
  console.log('Running scheduled horoscope fetch at 00:00 Sri Lankan time...');
  fetchAndSaveAllHoroscopes();
}, {
  scheduled: true,
  timezone: 'Asia/Colombo'
});

// Keep the process alive
setInterval(() => {
  // This keeps the Node.js process running
}, 60000); // Check every minute

// Telegram notification functions
async function sendTelegramNotification(message) {
  try {
    if (telegramBot && subscribedUsers.size > 0) {
      let successCount = 0;
      let failCount = 0;

      // Send to all subscribed users
      for (const chatId of subscribedUsers) {
        try {
          await telegramBot.sendMessage(chatId, message, {
            parse_mode: 'HTML',
            disable_web_page_preview: true
          });
          successCount++;
        } catch (error) {
          console.error(`Failed to send notification to chat ${chatId}:`, error.message);

          // Remove user if they blocked the bot or chat doesn't exist
          if (error.response && (error.response.statusCode === 403 || error.response.statusCode === 400)) {
            subscribedUsers.delete(chatId);
            console.log(`Removed inactive user: ${chatId}`);
          }
          failCount++;
        }
      }

      console.log(`Telegram notifications sent: ${successCount} successful, ${failCount} failed`);
      return successCount > 0;
    } else {
      console.log('Telegram notification (no subscribers):', message);
      return false;
    }
  } catch (error) {
    console.error('Failed to send Telegram notifications:', error.message);
    return false;
  }
}

// Send notification to specific user
async function sendTelegramNotificationToUser(chatId, message) {
  try {
    if (telegramBot) {
      await telegramBot.sendMessage(chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
      console.log(`Telegram notification sent to user ${chatId}`);
      return true;
    } else {
      console.log('Telegram bot not configured');
      return false;
    }
  } catch (error) {
    console.error(`Failed to send Telegram notification to ${chatId}:`, error.message);
    return false;
  }
}

// Format order details for Telegram
function formatOrderForTelegram(orderData) {
  const { customerInfo, items, totalAmount, orderId, timestamp } = orderData;

  let message = `🛒 <b>New Order Received!</b>\n\n`;
  message += `📋 <b>Order ID:</b> #${orderId}\n`;
  message += `📅 <b>Date:</b> ${new Date(timestamp).toLocaleString('en-US', { timeZone: 'Asia/Colombo' })}\n\n`;

  message += `👤 <b>Customer Information:</b>\n`;
  message += `• <b>Name:</b> ${customerInfo.fullName}\n`;
  message += `• <b>Phone 1:</b> ${customerInfo.phone1}\n`;
  if (customerInfo.phone2) {
    message += `• <b>Phone 2:</b> ${customerInfo.phone2}\n`;
  }
  message += `• <b>Zodiac Sign:</b> ${customerInfo.zodiacSign}\n`;
  message += `• <b>Address:</b> ${customerInfo.address}\n`;
  message += `• <b>City:</b> ${customerInfo.city}\n`;

  message += `\n🛍️ <b>Ordered Items:</b>\n`;
  items.forEach((item, index) => {
    message += `${index + 1}. <b>${item.name}</b>\n`;
    message += `   • Quantity: ${item.quantity}\n`;
    message += `   • Price: Rs.${item.price.toLocaleString()}\n`;
    message += `   • Subtotal: Rs.${(item.price * item.quantity).toLocaleString()}\n\n`;
  });

  message += `💰 <b>Total Amount:</b> Rs.${totalAmount.toLocaleString()}\n`;
  message += `💳 <b>Payment Method:</b> Cash on Delivery (COD)\n\n`;
  message += `⚡ Please process this order!`;

  return message;
}

// Order processing and storage
async function saveOrderToDatabase(orderData) {
  try {
    const data = await readDatabase();

    // Initialize orders array if it doesn't exist
    if (!data.orders) {
      data.orders = [];
    }

    // Add new order
    data.orders.push(orderData);

    await writeDatabase(data);
    console.log(`Order ${orderData.orderId} saved to database`);
    return true;
  } catch (error) {
    console.error('Error saving order to database:', error);
    throw error;
  }
}




// Get horoscope by sign with improved caching
app.get('/api/horoscope/:signId', async (req, res) => {
    try {
        const { signId } = req.params;
        const today = getSriLankanDate();
        const cacheKey = `horoscope_${signId.toLowerCase()}_${today}`;
        
        console.log(`[API] Horoscope request for ${signId} on ${today}`);
        
        // Check cache first
        let horoscope = cache.get(cacheKey);
        
        if (!horoscope) {
            console.log(`[API] No cache found, checking database for ${signId}`);
            // If not in cache, get from database
            const data = await readDatabase();
            console.log(`[API] Database contains ${data.horoscopes.length} horoscope entries`);
            
            const dbHoroscope = data.horoscopes.find(
                h => h.sign_id === signId.toLowerCase() && h.date_created === today
            );
            
            if (dbHoroscope) {
                console.log(`[API] Found horoscope in database for ${signId}`);
                horoscope = {
                    signId: dbHoroscope.sign_id,
                    signEnglish: dbHoroscope.sign_english,
                    signSinhala: dbHoroscope.sign_sinhala,
                    categories: {
                        love: dbHoroscope.love_content,
                        career: dbHoroscope.career_content,
                        health: dbHoroscope.health_content,
                        finance: dbHoroscope.finance_content,
                        general: dbHoroscope.general_content
                    },
                    rawContent: dbHoroscope.raw_content,
                    lastUpdated: dbHoroscope.created_at
                };
                
                // Cache the result
                cache.set(cacheKey, horoscope);
            } else {
                console.log(`[API] No horoscope found in database for ${signId} on ${today}`);
            }
        } else {
            console.log(`[API] Found horoscope in cache for ${signId}`);
        }
        
        if (horoscope) {
            res.json({
                success: true,
                data: horoscope,
                cached: cache.has(cacheKey)
            });
        } else {
            // Try to generate fresh horoscope if none exists
            console.log(`[API] No horoscope found for ${signId} on ${today}. Attempting to generate...`);
            const signData = zodiacSigns.find(s => s.id === signId.toLowerCase());
            if (!signData) {
                console.error(`[API] Invalid sign ID: ${signId}`);
                return res.status(400).json({ success: false, error: 'Invalid zodiac sign' });
            }
            
            try {
                await fetchAndSaveHoroscope(signData);
                console.log(`[API] Successfully generated horoscope for ${signId}`);
            } catch (genError) {
                console.error(`[API] Failed to generate horoscope for ${signId}:`, genError.message);
            }
            
            // Retry after generation
            console.log(`[API] Retrying database lookup for ${signId}`);
            const data = await readDatabase();
            const dbHoroscope = data.horoscopes.find(
                h => h.sign_id === signId.toLowerCase() && h.date_created === today
            );
            
            if (dbHoroscope) {
                console.log(`[API] Found generated horoscope for ${signId}`);
                horoscope = {
                    signId: dbHoroscope.sign_id,
                    signEnglish: dbHoroscope.sign_english,
                    signSinhala: dbHoroscope.sign_sinhala,
                    categories: {
                        love: dbHoroscope.love_content,
                        career: dbHoroscope.career_content,
                        health: dbHoroscope.health_content,
                        finance: dbHoroscope.finance_content,
                        general: dbHoroscope.general_content
                    },
                    rawContent: dbHoroscope.raw_content,
                    lastUpdated: dbHoroscope.created_at
                };
                
                cache.set(cacheKey, horoscope);
                res.json({
                    success: true,
                    data: horoscope,
                    cached: false,
                    freshlyGenerated: true
                });
            } else {
                console.error(`[API] Still no horoscope found after generation attempt for ${signId}`);
                res.status(404).json({ 
                    success: false, 
                    error: 'No horoscope found for today. Please try again later.',
                    debug: {
                        signId: signId,
                        today: today,
                        dbPath: dbPath
                    }
                });
            }
        }
    } catch (error) {
        console.error('[API] Database error in horoscope endpoint:', error.message);
        console.error('[API] Error stack:', error.stack);
        res.status(500).json({ 
            success: false, 
            error: 'Database error',
            debug: {
                message: error.message,
                dbPath: dbPath
            }
        });
    }
});

// Get specific category for a sign
app.get('/api/horoscope/:signId/:category', async (req, res) => {
  try {
    const { signId, category } = req.params;
    const today = getSriLankanDate();
    
    const validCategories = ['love', 'career', 'health', 'finance', 'general'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid category. Valid categories: ' + validCategories.join(', ') 
      });
    }
    
    const data = await readDatabase();
    const horoscope = data.horoscopes.find(
      h => h.sign_id === signId.toLowerCase() && h.date_created === today
    );
    
    if (horoscope) {
      const columnMap = {
        love: 'love_content',
        career: 'career_content',
        health: 'health_content',
        finance: 'finance_content',
        general: 'general_content'
      };
      
      res.json({
        success: true,
        data: {
          signId: horoscope.sign_id,
          signEnglish: horoscope.sign_english,
          signSinhala: horoscope.sign_sinhala,
          category: category,
          content: horoscope[columnMap[category]],
          lastUpdated: horoscope.created_at
        }
      });
    } else {
      res.status(404).json({ 
        success: false, 
        error: 'No horoscope found for today. Please try again later.' 
      });
    }
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Database error' 
    });
  }
});

// Get all horoscopes for today
app.get('/api/horoscopes/today', async (req, res) => {
  try {
    const today = getSriLankanDate();
    const data = await readDatabase();
    
    const todayHoroscopes = data.horoscopes
      .filter(h => h.date_created === today)
      .sort((a, b) => a.sign_id.localeCompare(b.sign_id));
    
    res.json({
      success: true,
      data: todayHoroscopes,
      count: todayHoroscopes.length
    });
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Manual refresh endpoint (for testing)
app.post('/api/refresh-horoscopes', async (req, res) => {
  try {
    // Clear cache before refreshing
    cache.flushAll();
    console.log('Cache cleared before refresh');
    
    await fetchAndSaveAllHoroscopes();
    res.json({ 
      success: true, 
      message: 'Horoscopes refreshed successfully',
      cacheCleared: true
    });
  } catch (error) {
    console.error('Manual refresh error:', error);
    res.status(500).json({ success: false, error: 'Failed to refresh horoscopes' });
  }
});

// Clear cache endpoint
app.post('/api/clear-cache', (req, res) => {
  try {
    const keys = cache.keys();
    cache.flushAll();
    res.json({ 
      success: true, 
      message: 'Cache cleared successfully',
      clearedKeys: keys.length
    });
  } catch (error) {
    console.error('Cache clear error:', error);
    res.status(500).json({ success: false, error: 'Failed to clear cache' });
  }
});

// Cache stats endpoint
app.get('/api/cache-stats', (req, res) => {
  try {
    const stats = cache.getStats();
    const keys = cache.keys();
    res.json({ 
      success: true, 
      stats: stats,
      totalKeys: keys.length,
      keys: keys,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Cache stats error:', error);
    res.status(500).json({ success: false, error: 'Failed to get cache stats' });
  }
});

// Force browser cache clear endpoint
app.post('/api/force-refresh', (req, res) => {
  try {
    // Clear server cache
    cache.flushAll();
    
    // Send headers to force browser cache clear
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store',
      'X-Accel-Expires': '0',
      'X-Cache-Timestamp': Date.now().toString(),
      'X-Force-Refresh': 'true'
    });
    
    res.json({ 
      success: true, 
      message: 'Cache cleared and browser refresh forced',
      timestamp: new Date().toISOString(),
      cacheVersion: Date.now()
    });
  } catch (error) {
    console.error('Force refresh error:', error);
    res.status(500).json({ success: false, error: 'Failed to force refresh' });
  }
});

// Clear today's data and fetch fresh horoscopes endpoint
app.post('/api/clear-and-refetch', async (req, res) => {
  try {
    const result = await clearTodayDataAndRefetch();
    res.json({ 
      success: true, 
      message: `Cleared ${result.removedCount} entries for ${result.date} and fetched fresh horoscopes`,
      removedCount: result.removedCount,
      date: result.date
    });
  } catch (error) {
    console.error('Clear and refetch error:', error);
    res.status(500).json({ success: false, error: 'Failed to clear and refetch horoscopes' });
  }
});

// Admin endpoint for clearing and refetching
app.post('/api/admin/clear-and-refetch', async (req, res) => {
  try {
    console.log('[ADMIN] Clear and refetch requested');
    const result = await clearTodayDataAndRefetch();
    res.json({ 
      success: true, 
      message: `Cleared ${result.removedCount} entries for ${result.date} and fetched fresh horoscopes`,
      removedCount: result.removedCount,
      date: result.date
    });
  } catch (error) {
    console.error('[ADMIN] Clear and refetch error:', error);
    res.status(500).json({ success: false, error: 'Failed to clear and refetch horoscopes' });
  }
});

// Admin endpoint for testing Gemini API
app.post('/api/admin/test-gemini', async (req, res) => {
  try {
    console.log('[ADMIN] Testing Gemini API');
    const testContent = await getHoroscopeFromGemini('Aries', 'මේෂ');
    res.json({ 
      success: true, 
      message: 'Gemini API test successful',
      content: testContent.substring(0, 500) + '...',
      fullLength: testContent.length
    });
  } catch (error) {
    console.error('[ADMIN] Gemini API test error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Gemini API test failed',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Horoscope API is running',
    timestamp: getSriLankanDateTime().toISOString()
  });
});

// Check if horoscopes need to be fetched
async function checkAndFetchHoroscopes() {
  try {
    const today = getSriLankanDate();
    const data = await readDatabase();
    const todayHoroscopes = data.horoscopes.filter(h => h.date_created === today);
    
    // Check if we have all 12 zodiac signs for today
    if (todayHoroscopes.length < 12) {
      console.log(`Found ${todayHoroscopes.length}/12 horoscopes for today. Fetching missing horoscopes...`);
      await fetchAndSaveAllHoroscopes();
    } else {
      console.log(`All horoscopes for today (${today}) are already available. Skipping fetch.`);
    }
  } catch (error) {
    console.error('Error checking existing horoscopes:', error);
    console.log('Running fetch as fallback...');
    await fetchAndSaveAllHoroscopes();
  }
}

// Start server
app.listen(PORT, async () => {
  console.log(`Horoscope server running on port ${PORT}`);
  console.log('Daily horoscope fetch scheduled for 00:00 AM Sri Lankan time (GMT+5:30)');
  
  // Initialize JSON database
  await initializeDatabase();
  
  // Check and fetch horoscopes only if needed
  await checkAndFetchHoroscopes();
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  console.log('JSON database saved.');
  process.exit(0);
});