class TranslationService {
  constructor() {
    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    this.cache = new Map();
    this.requestQueue = new Map();
  }

  // Language mappings for better context
  getLanguageInfo(languageKey) {
    const languages = {
      sinhala: {
        name: 'Sinhala',
        nativeName: 'සිංහල',
        code: 'si'
      },
      english: {
        name: 'English',
        nativeName: 'English',
        code: 'en'
      },
      tamil: {
        name: 'Tamil',
        nativeName: 'தமிழ்',
        code: 'ta'
      }
    };
    return languages[languageKey] || languages.english;
  }

  // Generate cache key
  getCacheKey(text, targetLanguage, context) {
    return `${text.substring(0, 50)}_${targetLanguage}_${context}`.replace(/\s+/g, '_');
  }

  // Translate text using backend Gemini API
  async translateText(text, targetLanguage, context = '') {
    if (!text || text.trim() === '') {
      return text;
    }

    // Return original text if target is Sinhala (default language)
    if (targetLanguage === 'sinhala') {
      return text;
    }

    const cacheKey = this.getCacheKey(text, targetLanguage, context);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Check if there's already a pending request for this translation
    if (this.requestQueue.has(cacheKey)) {
      return await this.requestQueue.get(cacheKey);
    }

    // Create the translation request
    const translationPromise = this.performTranslation(text, targetLanguage, context);
    this.requestQueue.set(cacheKey, translationPromise);

    try {
      const result = await translationPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.requestQueue.delete(cacheKey);
    }
  }

  // Perform the actual translation via backend API
  async performTranslation(text, targetLanguage, context = '') {
    try {
      const targetLangInfo = this.getLanguageInfo(targetLanguage);
      
      const response = await fetch(`${this.apiUrl}/translate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          targetLanguage: targetLanguage,
          targetLanguageName: targetLangInfo.name,
          context: context || 'horoscope_content'
        })
      });

      if (!response.ok) {
        throw new Error(`Translation API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.translatedText) {
        return data.translatedText;
      } else {
        throw new Error(data.error || 'Translation failed');
      }
    } catch (error) {
      console.error('Translation service error:', error);
      
      // Fallback: return original text if translation fails
      return text;
    }
  }

  // Translate multiple texts efficiently
  async translateMultiple(textArray, targetLanguage, context = '') {
    if (targetLanguage === 'sinhala') {
      return textArray;
    }

    try {
      const translations = await Promise.all(
        textArray.map(text => this.translateText(text, targetLanguage, context))
      );
      return translations;
    } catch (error) {
      console.error('Batch translation error:', error);
      return textArray; // Return original texts if batch translation fails
    }
  }

  // Translate horoscope categories
  async translateHoroscopeCategories(categories, targetLanguage) {
    if (targetLanguage === 'sinhala' || !categories || categories.length === 0) {
      return categories;
    }

    try {
      const translatedCategories = await Promise.all(
        categories.map(async (category) => {
          const translatedTitle = await this.translateText(
            category.title, 
            targetLanguage, 
            'horoscope_category_title'
          );
          
          const translatedContent = await this.translateText(
            category.content, 
            targetLanguage, 
            'horoscope_content'
          );

          return {
            ...category,
            title: translatedTitle,
            content: translatedContent
          };
        })
      );

      return translatedCategories;
    } catch (error) {
      console.error('Horoscope categories translation error:', error);
      return categories; // Return original categories if translation fails
    }
  }

  // Clear cache (useful for memory management)
  clearCache() {
    this.cache.clear();
  }

  // Get cache size for debugging
  getCacheSize() {
    return this.cache.size;
  }
}

const translationService = new TranslationService();
export default translationService;
